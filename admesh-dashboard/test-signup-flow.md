# Signup Flow Test Plan

## Test Cases to Verify Fixed Signup Flow

### 1. Brand Signup - Website Already Exists
**Expected Behavior:** Validation should fail BEFORE Firebase user creation
- Enter existing website domain
- Submit form
- Should show error message without creating Firebase user
- No orphaned Firebase accounts

### 2. Agent Signup - Name Already Exists  
**Expected Behavior:** Validation should fail BEFORE Firebase user creation
- Enter existing agent name
- Submit form
- Should show error message without creating Firebase user
- No orphaned Firebase accounts

### 3. Brand Signup - Invalid Website
**Expected Behavior:** Validation should fail BEFORE Firebase user creation
- Enter inaccessible website
- Submit form
- Should show error message without creating Firebase user
- No orphaned Firebase accounts

### 4. Backend Registration Failure (Simulated)
**Expected Behavior:** Firebase user should be cleaned up if backend fails
- Valid form data that passes frontend validation
- Backend returns error (simulated by temporarily breaking backend)
- Firebase user should be created then immediately deleted
- No orphaned Firebase accounts

### 5. Successful Registration
**Expected Behavior:** Complete flow should work normally
- Valid form data
- All validations pass
- Firebase user created
- Backend registration succeeds
- User redirected to dashboard

## Manual Testing Steps

1. **Test Website Existence Check:**
   ```
   - Go to signup page
   - Select "Brand" role
   - Enter existing website (e.g., google.com)
   - Try to submit
   - Verify error appears before Firebase user creation
   ```

2. **Test Agent Name Check:**
   ```
   - Go to signup page  
   - Select "Agent" role
   - Enter existing agent name
   - Try to submit
   - Verify error appears before Firebase user creation
   ```

3. **Test Backend Failure Cleanup:**
   ```
   - Temporarily modify backend to return error
   - Complete valid signup form
   - Submit and verify Firebase user gets cleaned up
   - Restore backend functionality
   ```

## Verification Points

- [ ] Frontend validation happens BEFORE Firebase user creation
- [ ] Website existence check prevents Firebase user creation
- [ ] Agent name existence check prevents Firebase user creation  
- [ ] Backend registration failure triggers Firebase user cleanup
- [ ] No orphaned Firebase users remain after any failure scenario
- [ ] Successful registrations work normally
- [ ] Error messages are user-friendly and specific
