import unittest
import os
import sys
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.utils.embedding import embed_text, cosine_similarity


class TestEnhancedEmbeddings(unittest.TestCase):
    """Test cases for enhanced embedding functionality in active offer matching"""

    def test_semantic_similarity_filtering(self):
        """Test that semantic similarity filtering works correctly"""
        # Test vectors with known similarity scores
        query_embedding = [1.0, 0.0, 0.0, 0.0]  # Base vector

        test_cases = [
            {
                "product_embedding": [1.0, 0.0, 0.0, 0.0],  # Identical
                "should_pass_threshold": True
            },
            {
                "product_embedding": [0.8, 0.6, 0.0, 0.0],  # High similarity (cos = 0.8)
                "should_pass_threshold": True
            },
            {
                "product_embedding": [0.0, 1.0, 0.0, 0.0],  # Orthogonal (cos = 0.0)
                "should_pass_threshold": False
            },
            {
                "product_embedding": [0.5, 0.5, 0.5, 0.5],  # Medium similarity (cos = 0.5)
                "should_pass_threshold": True
            },
            {
                "product_embedding": [0.1, 0.9, 0.1, 0.1],  # Low similarity
                "should_pass_threshold": False
            }
        ]

        THRESHOLD = 0.3  # Same as in our implementation

        for i, case in enumerate(test_cases):
            similarity = cosine_similarity(query_embedding, case["product_embedding"])

            # Check if it would pass our threshold
            passes_threshold = similarity >= THRESHOLD
            self.assertEqual(passes_threshold, case["should_pass_threshold"],
                           f"Test case {i}: Similarity {similarity:.3f} should {'pass' if case['should_pass_threshold'] else 'fail'} threshold {THRESHOLD}")

            # Verify similarity is in valid range
            self.assertGreaterEqual(similarity, 0.0)
            self.assertLessEqual(similarity, 1.0)

    @patch('api.utils.embedding.client.embeddings.create')
    def test_comprehensive_embedding_generation(self, mock_openai_create):
        """Test comprehensive embedding generation for products without embeddings"""
        # Mock the OpenAI response
        mock_response = MagicMock()
        mock_response.data = [MagicMock()]
        mock_response.data[0].embedding = [0.1, 0.2, 0.3, 0.4, 0.5] * 307  # 1535 dimensions
        mock_openai_create.return_value = mock_response

        # Test product data (simulating what would be found in fetch_active_offers)
        product_data = {
            "title": "CRM Software",
            "description": "Customer relationship management platform",
            "categories": ["CRM", "Sales", "Business"],
            "keywords": ["crm", "sales", "customers", "leads", "pipeline"],
            "audience_segment": "Small Business",
            "integration_list": ["Slack", "Email", "Calendar"]
        }

        # Simulate the embedding text construction from fetch_active_offers
        embedding_text_parts = []
        
        if product_data.get("title"):
            embedding_text_parts.append(f"Product: {product_data['title']}")
        if product_data.get("description"):
            embedding_text_parts.append(f"Description: {product_data['description']}")
        if product_data.get("categories") and isinstance(product_data["categories"], list):
            categories_text = ", ".join(product_data["categories"])
            embedding_text_parts.append(f"Categories: {categories_text}")
        if product_data.get("keywords") and isinstance(product_data["keywords"], list):
            keywords_text = ", ".join(product_data["keywords"])
            embedding_text_parts.append(f"Keywords: {keywords_text}")
        if product_data.get("audience_segment"):
            embedding_text_parts.append(f"Target audience: {product_data['audience_segment']}")
        if product_data.get("integration_list") and isinstance(product_data["integration_list"], list):
            integrations_text = ", ".join(product_data["integration_list"])
            embedding_text_parts.append(f"Integrations: {integrations_text}")
        
        embedding_text = ". ".join(embedding_text_parts)
        
        # Generate embedding
        result = embed_text(embedding_text)

        # Verify comprehensive embedding text was created
        expected_text = "Product: CRM Software. Description: Customer relationship management platform. Categories: CRM, Sales, Business. Keywords: crm, sales, customers, leads, pipeline. Target audience: Small Business. Integrations: Slack, Email, Calendar"
        
        # Verify OpenAI was called with the comprehensive text
        mock_openai_create.assert_called_once_with(
            input=expected_text,
            model="text-embedding-3-small"
        )
        
        # Verify result
        self.assertEqual(len(result), 1535)

    def test_embedding_fallback_mechanism(self):
        """Test that fallback embedding works when comprehensive embedding fails"""
        # This test simulates the fallback logic in fetch_active_offers
        product_data = {
            "title": "Test Product",
            "description": "Test description",
            "categories": None,  # This might cause issues
            "keywords": [],
            "audience_segment": "",
            "integration_list": None
        }

        # Simulate the comprehensive embedding text construction
        embedding_text_parts = []
        
        if product_data.get("title"):
            embedding_text_parts.append(f"Product: {product_data['title']}")
        if product_data.get("description"):
            embedding_text_parts.append(f"Description: {product_data['description']}")
        if product_data.get("categories") and isinstance(product_data["categories"], list):
            categories_text = ", ".join(product_data["categories"])
            embedding_text_parts.append(f"Categories: {categories_text}")
        if product_data.get("keywords") and isinstance(product_data["keywords"], list):
            keywords_text = ", ".join(product_data["keywords"])
            embedding_text_parts.append(f"Keywords: {keywords_text}")
        if product_data.get("audience_segment"):
            embedding_text_parts.append(f"Target audience: {product_data['audience_segment']}")
        if product_data.get("integration_list") and isinstance(product_data["integration_list"], list):
            integrations_text = ", ".join(product_data["integration_list"])
            embedding_text_parts.append(f"Integrations: {integrations_text}")
        
        embedding_text = ". ".join(embedding_text_parts)
        
        # Should still create valid text with just title and description
        expected_text = "Product: Test Product. Description: Test description"
        self.assertEqual(embedding_text, expected_text)

    def test_threshold_boundary_conditions(self):
        """Test edge cases around the similarity threshold"""
        query_embedding = [1.0, 0.0, 0.0]
        THRESHOLD = 0.3

        # Create test vectors that will produce specific similarity scores
        # For cosine similarity with [1,0,0], we need vectors where the first component
        # divided by the magnitude equals our target similarity

        # Vector that should give similarity around 0.3
        low_sim_vector = [0.3, 0.954, 0.0]  # cos ≈ 0.3
        # Vector that should give similarity around 0.35
        high_sim_vector = [0.35, 0.937, 0.0]  # cos ≈ 0.35
        # Vector that should give similarity of 0.0
        zero_sim_vector = [0.0, 1.0, 0.0]  # cos = 0.0

        boundary_cases = [
            (low_sim_vector, "around_threshold"),
            (high_sim_vector, "above_threshold"),
            (zero_sim_vector, "below_threshold"),
        ]

        for product_embedding, case_type in boundary_cases:
            similarity = cosine_similarity(query_embedding, product_embedding)
            passes_threshold = similarity >= THRESHOLD

            if case_type == "around_threshold":
                # Should be close to threshold
                self.assertAlmostEqual(similarity, THRESHOLD, places=1)
            elif case_type == "above_threshold":
                # Should be above threshold
                self.assertGreater(similarity, THRESHOLD)
                self.assertTrue(passes_threshold)
            elif case_type == "below_threshold":
                # Should be below threshold
                self.assertLess(similarity, THRESHOLD)
                self.assertFalse(passes_threshold)


if __name__ == '__main__':
    # Set environment variable for testing
    os.environ['OPENAI_API_KEY'] = 'test-key'
    
    unittest.main()
