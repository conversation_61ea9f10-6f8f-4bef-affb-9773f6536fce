import unittest
import os
import sys
from unittest.mock import patch, MagicMock
import json

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.routes.agent_recommendation import fetch_fallback_offer, fetch_active_offers


class TestAgentRecommendationFallback(unittest.TestCase):
    """Test cases for agent recommendation fallback functionality"""

    @patch('api.routes.agent_recommendation.db')
    def test_fetch_fallback_offer_success(self, mock_db):
        """Test successful fallback offer retrieval"""
        # Mock Firestore collection and query
        mock_collection = MagicMock()
        mock_query = MagicMock()
        mock_doc = MagicMock()
        
        # Setup mock document data
        mock_doc.id = "test_offer_id"
        mock_doc.to_dict.return_value = {
            "product_id": "test_product_id",
            "active": True,
            "offer_trust_score": 0.8,
            "brand_trust_score": 0.9,
            "payout": {"amount": 50.0},
            "keywords": ["test", "software"],
            "categories": ["software"],
            "url": "https://example.com",
            "redirect_url": "https://example.com/redirect"
        }
        
        # Mock product document
        mock_product_doc = MagicMock()
        mock_product_doc.exists = True
        mock_product_doc.to_dict.return_value = {
            "title": "Test Product",
            "description": "A test product for fallback",
            "features": ["feature1", "feature2"],
            "pricing": "$10/month",
            "has_free_tier": True,
            "trial_days": 14
        }
        
        # Setup mock chain
        mock_db.collection.return_value = mock_collection
        mock_collection.where.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.stream.return_value = [mock_doc]
        mock_collection.document.return_value.get.return_value = mock_product_doc
        mock_collection.document.return_value.update = MagicMock()
        
        # Call the function
        result = fetch_fallback_offer(is_test=True)
        
        # Assertions
        self.assertIsNotNone(result)
        self.assertEqual(result["title"], "Test Product")
        self.assertEqual(result["offer_id"], "test_offer_id")
        self.assertEqual(result["product_id"], "test_product_id")
        self.assertTrue(result["is_fallback"])
        # Check that it uses the main reason logic (should mention trust, competitive rewards, free tier, trial)
        self.assertIn("highly trusted platform", result["match_reason"])
        self.assertIn("competitive rewards", result["match_reason"])
        self.assertIn("offers free tier", result["match_reason"])
        self.assertIn("14 day trial available", result["match_reason"])
        
        # Verify the score calculation (70% trust + 30% payout)
        expected_trust = (0.8 + 0.9) / 2  # 0.85
        expected_payout_score = min(50.0 / 100, 1.0)  # 0.5
        expected_score = (0.7 * expected_trust) + (0.3 * expected_payout_score)
        self.assertAlmostEqual(result["intent_match_score"], expected_score, places=4)

    @patch('api.routes.agent_recommendation.db')
    def test_fetch_fallback_offer_no_active_offers(self, mock_db):
        """Test fallback when no active offers exist"""
        # Mock empty query result
        mock_collection = MagicMock()
        mock_query = MagicMock()
        
        mock_db.collection.return_value = mock_collection
        mock_collection.where.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.stream.return_value = []  # No offers
        
        # Call the function
        result = fetch_fallback_offer(is_test=True)
        
        # Should return None when no offers available
        self.assertIsNone(result)

    @patch('api.routes.agent_recommendation.db')
    def test_fetch_fallback_offer_missing_product(self, mock_db):
        """Test fallback when offer has missing product"""
        # Mock offer without valid product
        mock_collection = MagicMock()
        mock_query = MagicMock()
        mock_doc = MagicMock()
        
        mock_doc.id = "test_offer_id"
        mock_doc.to_dict.return_value = {
            "product_id": "missing_product_id",
            "active": True,
            "offer_trust_score": 0.8,
            "brand_trust_score": 0.9,
            "payout": 50.0
        }
        
        # Mock non-existent product
        mock_product_doc = MagicMock()
        mock_product_doc.exists = False
        
        mock_db.collection.return_value = mock_collection
        mock_collection.where.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.stream.return_value = [mock_doc]
        mock_collection.document.return_value.get.return_value = mock_product_doc
        
        # Call the function
        result = fetch_fallback_offer(is_test=True)
        
        # Should return None when product doesn't exist
        self.assertIsNone(result)

    @patch('api.routes.agent_recommendation.fetch_fallback_offer')
    @patch('api.routes.agent_recommendation.embed_text')
    @patch('api.routes.agent_recommendation.db')
    def test_fallback_integration_with_fetch_active_offers(self, mock_db, mock_embed_text, mock_fetch_fallback):
        """Test that fallback is called when fetch_active_offers returns empty"""
        # Mock empty result from main query
        mock_collection = MagicMock()
        mock_query = MagicMock()
        
        mock_db.collection.return_value = mock_collection
        mock_collection.where.return_value = mock_query
        mock_query.stream.return_value = []  # No matching offers
        
        # Mock embedding
        mock_embed_text.return_value = [0.1, 0.2, 0.3]
        
        # Mock fallback offer
        mock_fallback_offer = {
            "title": "Fallback Product",
            "offer_id": "fallback_offer_id",
            "product_id": "fallback_product_id",
            "is_fallback": True,
            "intent_match_score": 0.6,
            "match_reason": "Fallback recommendation"
        }
        mock_fetch_fallback.return_value = mock_fallback_offer
        
        # Call fetch_active_offers
        result = fetch_active_offers(
            categories=["software"],
            keywords=["test"],
            llm_confidence=0.8,
            query_embedding=[0.1, 0.2, 0.3],
            is_test=True
        )
        
        # Should return empty list (fallback is handled at higher level)
        self.assertEqual(len(result), 0)

    def test_fallback_scoring_calculation(self):
        """Test the fallback scoring calculation logic"""
        # Test case 1: High trust, high payout
        trust_offer = 0.9
        trust_brand = 0.8
        payout_amount = 100.0
        
        combined_trust = (trust_offer + trust_brand) / 2  # 0.85
        payout_score = min(payout_amount / 100, 1.0)  # 1.0
        expected_score = (0.7 * combined_trust) + (0.3 * payout_score)
        # 0.7 * 0.85 + 0.3 * 1.0 = 0.595 + 0.3 = 0.895
        
        self.assertAlmostEqual(expected_score, 0.895, places=3)
        
        # Test case 2: Low trust, low payout
        trust_offer = 0.3
        trust_brand = 0.4
        payout_amount = 10.0
        
        combined_trust = (trust_offer + trust_brand) / 2  # 0.35
        payout_score = min(payout_amount / 100, 1.0)  # 0.1
        expected_score = (0.7 * combined_trust) + (0.3 * payout_score)
        # 0.7 * 0.35 + 0.3 * 0.1 = 0.245 + 0.03 = 0.275
        
        self.assertAlmostEqual(expected_score, 0.275, places=3)


if __name__ == '__main__':
    # Set environment variables for testing
    os.environ['OPENAI_API_KEY'] = 'test-key'
    os.environ['ENVIRONMENT'] = 'test'
    
    unittest.main()
