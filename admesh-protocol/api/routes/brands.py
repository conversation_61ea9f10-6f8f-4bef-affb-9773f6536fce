import uuid
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from firebase.config import get_db
from typing import Optional, Literal, List, Union
import os
import json
import requests
import logging
from urllib.parse import urlparse
from auth.deps import require_role, verify_firebase_token
from google.cloud import firestore
from slugify import slugify

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

# Helper function to check if we're in development environment
def is_development_environment() -> bool:
    """
    Check if we're running in development environment
    """
    env = os.getenv("ENV", "development").lower()
    return env == "development"

# Helper function to create wallet transaction logs
def create_wallet_transaction(
    brand_id: str,
    transaction_type: Literal["credit", "debit"],
    category: str,
    amount: float,
    description: str,
    balance_after: float,
    reference_id: Optional[str] = None,
    reference_type: Optional[str] = None
) -> str:
    """
    Create a transaction log in the wallet's transactions subcollection

    Args:
        brand_id: The ID of the brand
        transaction_type: The type of transaction (credit or debit)
        category: The category of the transaction (e.g., promo_credit, budget_allocation)
        amount: The amount of the transaction in cents
        description: A description of the transaction
        balance_after: The balance after the transaction
        reference_id: An optional reference ID (e.g., offer_id)
        reference_type: An optional reference type (e.g., offer)

    Returns:
        The ID of the created transaction document
    """
    transaction_data = {
        "type": transaction_type,
        "category": category,
        "amount": amount,
        "description": description,
        "timestamp": firestore.SERVER_TIMESTAMP,
        "balance_after": balance_after,
        "reference_id": reference_id,
        "reference_type": reference_type
    }

    # Add the transaction to the wallet's transactions subcollection
    transaction_ref = db.collection("wallets").document(brand_id).collection("transactions").add(transaction_data)

    # Return the ID of the created transaction
    return transaction_ref[1].id

# Admin endpoint to get all brands
@router.get("/admin/all")
async def admin_get_all_brands(
    limit: int = Query(50, description="Maximum number of brands to return"),
    offset: int = Query(0, description="Number of brands to skip"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_direction: str = Query("desc", description="Sort direction (asc or desc)"),
    include_wallet: bool = Query(False, description="Whether to include wallet information"),
    decoded_token = Depends(verify_firebase_token)
):
    """Admin endpoint to get all brands"""
    # Check if the user is an admin
    is_admin = decoded_token.get("admin", False)
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        # Start with base query
        query = db.collection("brands")

        # Apply sorting if provided
        if sort_by in ["created_at", "updated_at", "company_name", "offers_submitted", "budget_used", "budget_remaining"]:
            direction = firestore.Query.DESCENDING if sort_direction == "desc" else firestore.Query.ASCENDING
            query = query.order_by(sort_by, direction=direction)

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute query
        snapshot = query.stream()

        brands = []
        for doc in snapshot:
            brand_data = doc.to_dict()
            # Ensure id is included
            if "uid" not in brand_data:
                brand_data["uid"] = doc.id

            # Include wallet information if requested
            if include_wallet:
                wallet_doc = db.collection("wallets").document(doc.id).get()
                if wallet_doc.exists:
                    brand_data["wallet"] = wallet_doc.to_dict()
                else:
                    # Create default wallet data
                    default_wallet = {
                        "brand_id": doc.id,
                        "total_available_balance": 0.0,
                        "total_promo_available_balance": 5000.0,
                        "total_promo_balance_spent": 0.0,
                        "total_balance_spent": 0.0,
                        "total_budget_allocated": 0.0  # Track total budget allocated to campaigns
                    }
                    brand_data["wallet"] = default_wallet

            brands.append(brand_data)

        # Get total count for pagination
        total_count = len(list(db.collection("brands").select([]).stream()))

        return {
            "status": "success",
            "brands": brands,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch brands: {str(e)}")

# Import the promo credit model
from api.models.promo import PromoCredit
from datetime import datetime, timedelta
from firebase_admin import firestore
# Base brand structure
def default_brand_profile(uid: str, name: str) -> dict:
    # Initialize promo credit for free plan
    promo_credit = PromoCredit.create_free_plan_promo(datetime.now())

    return {
        "company_name": name,
        "uid": uid,
        "offers_submitted": 0,
        "budget_used": 0.0,
        "budget_remaining": 0.0,
        "verified": False,
        "website": None,
        "logo_url": None,
        "industry": None,
        "billing_country": None,
        "payment_method": None,
        "account_manager": None,
        "active_offers": [],
        "active_products": [],
        "inactive_offers": [],
        "inactive_products": [],
        "created_at": firestore.SERVER_TIMESTAMP,
        "onboarding_steps": {
            "brand": False,
            "product": False,
            "offer": False,
            "tracking": False  # Note: This was previously named "integration" in some places
        },
        "onboarding_status": "brand",
        # Add promo credit information
        "promo": promo_credit
    }

# Create brand profile
class BrandCreateRequest(BaseModel):
    brand_name: str
    website: str | None = None
    logo_url: str | None = None
    work_email: str | None = None
    headquarters: str | None = None

@router.post("/brands/create")
async def create_brand(payload: BrandCreateRequest, user=Depends(require_role("brand"))):
    uid = user["uid"]

    brand_ref = db.collection("brands").document(uid)
    if brand_ref.get().exists:
        return {"status": "already_exists"}

    base_profile = default_brand_profile(uid, payload.brand_name)

    enriched_fields = {
        "company_name": payload.brand_name,
        "website": payload.website,
        "logo_url": payload.logo_url,
        "work_email": payload.work_email,
        "headquarters": payload.headquarters,
    }

    brand_ref.set({**base_profile, **{k: v for k, v in enriched_fields.items() if v is not None}})

    # Create a separate wallet document for the brand
    wallet_data = {
        "brand_id": uid,
        "total_available_balance": 0.0,
        "total_promo_available_balance": 5000.0,  # $50 in cents
        "total_promo_balance_spent": 0.0,
        "total_balance_spent": 0.0,
        "total_budget_allocated": 0.0,  # Track total budget allocated to campaigns
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }
    db.collection("wallets").document(uid).set(wallet_data)

    return {"status": "success", "brand_id": uid}

# Update brand profile
class BrandUpdateRequest(BaseModel):
    brand_name: str | None = None
    website: str | None = None
    logo_url: str | None = None
    work_email: str | None = None
    headquarters: str | None = None
    industry: str | None = None
    billing_country: str | None = None
    payment_method: str | None = None
    description: str | None = None
    enriched_from_llm: bool | None = None

@router.post("/brands/update")
async def update_brand(payload: BrandUpdateRequest, user=Depends(require_role("brand"))):
    brand_id = user["uid"]

    brand_ref = db.collection("brands").document(brand_id)
    if not brand_ref.get().exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    update_data = {k: v for k, v in payload.model_dump().items() if v is not None}
    update_data["updated_at"] = firestore.SERVER_TIMESTAMP

    brand_ref.update(update_data)
    return {"status": "success", "updated_fields": list(update_data.keys())}


class IntelRequest(BaseModel):
    website: str


def get_site_url():
    return os.getenv("SITE_URL", "https://useadmesh.com")

def call_openrouter(prompt: str, model: str = "mistralai/mistral-7b-instruct") -> str:
    headers = {
        "Authorization": f"Bearer {os.getenv('OPENROUTER_API_KEY')}",
        "Content-Type": "application/json",
        "HTTP-Referer": get_site_url(),
        "X-Title": os.getenv("SITE_TITLE", "AdMesh")
    }

    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]
            }
        ]
    }
    try:
        res = requests.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=payload)
        res.raise_for_status()
        return res.json()["choices"][0]["message"]["content"]
    except requests.exceptions.RequestException as e:
        print("❌ OpenRouter API Error:", e)
        raise


# Public brand analysis endpoint for interactive demo
class BrandAnalysisRequest(BaseModel):
    website: str

@router.post("/api/demo/analyze")
async def analyze_brand_for_demo(req: BrandAnalysisRequest):
    """
    Public endpoint for brand analysis used in interactive demos.
    Does not require authentication.
    """
    prompt = f"""
    You are a JSON-only API. Given the website "{req.website}", return a valid minified JSON with the following fields:

    - title (product/brand name)
    - description (brief description of the product/service)
    - company_name (company name)
    - url (normalized website URL)
    - pricing (pricing information like "$29/month" or "Free with paid plans")
    - categories (array of strings, max 3 categories like ["E-commerce", "SaaS", "Business Tools"])
    - keywords (array of min 5 lowercase strings)
    - features (array of key features, max 5)
    - industry (industry category)
    - target_audience (target audience description)
    - value_proposition (main value proposition)
    - pricing_model (e.g., "Monthly subscription", "Pay-per-use", "Freemium")
    - has_free_tier (boolean)
    - trial_days (number or null)
    - trust_indicators (array of trust signals like ["SOC 2 certified", "99.9% uptime"])

    STRICT RULES:
    - No markdown.
    - No code blocks.
    - No text explanations.
    - Only return valid JSON.
    - categories must be an array with max 3 items
    - keywords must be an array with min 5 items
    - features must be an array with max 5 items
    - trust_indicators must be an array with max 3 items
    """

    try:
        llm_response = call_openrouter(prompt)
        print("🧠 LLM Response (raw):", repr(llm_response))

        data = json.loads(llm_response.strip())
        data["enriched_from_llm"] = True

        return data

    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return {"error": "Failed to parse LLM response"}
    except Exception as e:
        print(f"❌ Brand analysis error: {e}")
        return {"error": "Failed to analyze website"}

@router.post("/api/onboard/intel")
async def fetch_brand_intel(req: IntelRequest, user_data=Depends(require_role("brand"))):
    prompt = f"""
    You are a JSON-only API. Given the website "{req.website}", return a valid minified JSON with the following fields:

    - title (product/brand name)
    - description
    - url (normalized website URL)
    - categories (array of strings, max 3 categories like ["Marketing", "Development Tools", "SaaS"])
    - keywords (array of min 5 lowercase strings)
    - pricing_url (e.g., "{req.website}/pricing", "{req.website}/plans")
    - audience_segment (e.g., "Startups", "Agencies", "Developers", "Enterprise", "SMBs", "Freelancers")
    - integration_list (array of strings, e.g., ["Slack", "Zapier", "Stripe"])

    STRICT RULES:
    - No markdown.
    - No code blocks.
    - No text explanations.
    - Only return valid JSON.
    - categories must be an array with max 3 items
    - keywords must be an array with min 5 items
    """

    try:
        llm_response = call_openrouter(prompt)
        print("🧠 LLM Response (raw):", repr(llm_response))

        data = json.loads(llm_response.strip())  # No sanitization!
        data["enriched_from_llm"] = True

        # Check if a product with similar details already exists
        website_url = data.get("url", req.website)
        product_title = data.get("title", "")

        # Import and use normalize_url from openrouter.py
        from api.routes.openrouter import normalize_url
        normalized_url = normalize_url(website_url)

        # Generate slug from product title
        slug = slugify(product_title.lower())

        # Add normalized_url and slug to response
        data["normalized_url"] = normalized_url
        data["slug"] = slug

        # Check if a product with the same slug exists
        slug_query = db.collection("products").where("slug", "==", slug).limit(1).stream()
        existing_products_by_slug = list(slug_query)

        # If no match by slug, check by normalized URL
        if not existing_products_by_slug:
            url_query = db.collection("products").where("normalized_url", "==", normalized_url).limit(1).stream()
            existing_products_by_slug = list(url_query)

        # Fallback: check by regular URL
        if not existing_products_by_slug:
            fallback_url_query = db.collection("products").where("url", "==", website_url).limit(1).stream()
            existing_products_by_slug = list(fallback_url_query)

        # Add product_exists flag to response
        data["product_exists"] = len(existing_products_by_slug) > 0

        # If product exists, add a note
        if data["product_exists"]:
            existing_product = existing_products_by_slug[0].to_dict()
            data["existing_product_title"] = existing_product.get("title", product_title)
            print(f"Product already exists for website {req.website}")

        return data

    except json.JSONDecodeError as e:
        print("❌ JSON parsing failed:", e)
        return {
            "error": "Failed to parse JSON",
            "enriched_from_llm": False,
            "product_exists": False
        }
    except Exception as e:
        print("❌ General error:", e)
        return {
            "error": "Unexpected failure",
            "enriched_from_llm": False,
            "product_exists": False
        }


# --- Pydantic Schemas ---

class Payout(BaseModel):
    amount: float
    currency: str = "USD"
    model: str = "CPA"

class OfferIncentive(BaseModel):
    type: str  # "discount", "bonus", "free_trial", "credit", "extended_plan"
    headline: str
    details: str
    cta_label: str

class OfferPayload(BaseModel):
    goal: str
    payout: Payout
    offer_total_budget_allocated: float
    title: str
    description: str
    url: str
    categories: Optional[List[str]] = []
    suggestion_reason: Optional[str]
    reward_note: Optional[str]
    active: Optional[bool] = False  # Set offers as inactive by default
    promo_applied: Optional[bool] = False  # Flag to indicate if promo credit should be applied
    offer_incentive: Optional[OfferIncentive] = None  # Optional offer incentive

class ProductPayload(BaseModel):
    title: str
    url: str
    description: str
    categories: Optional[List[str]] = []  # Changed from single category to multiple categories
    keywords: List[str]
    pricing_url: Optional[str] = None
    audience_segment: Optional[str] = None
    integration_list: Optional[List[str]] = None

class BrandPayload(BaseModel):
    website: str
    brand_name: str
    logo_url: str
    work_email: Optional[str]
    headquarters: Optional[str]
    application_type: str

class TrackingPayload(BaseModel):
    method: str  # redirect_pixel | server_api | manual
    redirect_url: Optional[str]
    target_urls: Optional[List[str]] = []
    webhook_url: Optional[str]
    notes: Optional[str]

class OnboardingPayload(BaseModel):
    brand: BrandPayload
    product: ProductPayload
    offer: OfferPayload
    tracking: TrackingPayload

# Using require_role from auth.deps instead of custom function

# --- Route ---
@router.post("/onboarding/setup")
async def full_onboarding(
    payload: OnboardingPayload,
    user_data=Depends(require_role("brand"))
):
    db = get_db()
    brand_id = user_data["uid"]

    # 1. BRAND
    brand_dict = payload.brand.model_dump()
    brand_dict.update({
        "brand_id": brand_id,
        "updated_at": firestore.SERVER_TIMESTAMP,
        "enriched_from_llm": False,
        "verified": False,
        "active_products": [],
        "inactive_products": [],
    })
    db.collection("brands").document(brand_id).set(brand_dict, merge=True)

    # Get the promo_applied flag from the offer
    promo_applied = payload.offer.promo_applied if hasattr(payload.offer, "promo_applied") else False

    # Create a separate wallet document for the brand
    wallet_data = {
        "brand_id": brand_id,
        "total_available_balance": 0.0,
        "total_budget_allocated": 5000.0 if promo_applied else 0.0,  # Track total budget allocated to campaigns
        "total_promo_available_balance": 0.0,  # $50 in cents if promo is applied
        "total_promo_balance_used": 5000.0 if promo_applied else 0.0,
        "total_balance_spent": 0.0,
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }
    db.collection("wallets").document(brand_id).set(wallet_data)

    # Create transaction logs collection if it doesn't exist
    db.collection("wallets").document(brand_id).collection("transactions")

    # Create a transaction log for promo credit if applied
    if promo_applied:
        # First transaction: Add promo credit (credit transaction)
        create_wallet_transaction(
            brand_id=brand_id,
            transaction_type="credit",
            category="promo_credit",
            amount=5000.0,  # $50 in cents
            description="Promotional credit added during onboarding",
            balance_after=5000.0,
            reference_id=None,
            reference_type=None
        )

    # Update onboarding steps
    db.collection("brands").document(brand_id).update({
        "onboarding_steps.brand": True
    })

    # 2. PRODUCT
    product_dict = payload.product.model_dump()

    # Generate slug from title (ensure it's lowercase)
    title = product_dict.get("title", "")
    slug = slugify(title.lower())

    # Import and use normalize_url from openrouter.py
    from api.routes.openrouter import normalize_url
    raw_url = product_dict.get("url", "")
    normalized_url = normalize_url(raw_url)

    # Check if a product with the same slug exists
    slug_query = db.collection("products").where("slug", "==", slug).limit(1).stream()
    existing_products_by_slug = list(slug_query)

    # If no match by slug, check by normalized URL
    if not existing_products_by_slug:
        url_query = db.collection("products").where("normalized_url", "==", normalized_url).limit(1).stream()
        existing_products_by_slug = list(url_query)

    # Fallback: check by regular URL if no normalized URL matches found
    if not existing_products_by_slug:
        fallback_url_query = db.collection("products").where("url", "==", raw_url).limit(1).stream()
        existing_products_by_slug = list(fallback_url_query)

    if existing_products_by_slug:
        # Product with same URL/slug exists, update ONLY the brand_id
        existing_product = existing_products_by_slug[0]
        product_id = existing_product.id

        # Update the existing product with the new brand_id
        product_ref = db.collection("products").document(product_id)
        product_ref.update({"brand_id": brand_id})

        # Log that we're updating an existing product
        print(f"Product with slug/URL already exists. Updated brand_id for product: {product_id} to brand {brand_id}")
    else:
        # Create a new product
        product_id = str(uuid.uuid4())

        product_dict.update({
            "product_id": product_id,
            "brand_id": brand_id,
            "slug": slug,
            "normalized_url": normalized_url,
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        })

        # Ensure categories is a list (convert from single category if needed)
        if "category" in product_dict and "categories" not in product_dict:
            product_dict["categories"] = [product_dict["category"]]
            del product_dict["category"]  # Remove the old single category field
        elif "categories" not in product_dict:
            product_dict["categories"] = []

        db.collection("products").document(product_id).set(product_dict)

    db.collection("brands").document(brand_id).update({
        "active_products": firestore.ArrayUnion([product_id])
    })

    # Update onboarding steps
    db.collection("brands").document(brand_id).update({
        "onboarding_steps.product": True
    })

    # 3. OFFER
    offer_id = str(uuid.uuid4())
    offer_dict = payload.offer.model_dump()

    # Get the promo_applied flag from the payload
    promo_applied = offer_dict.get("promo_applied", False)

    # Set budget values based on promo_applied flag
    total_promo_available = 5000.0 if promo_applied else 0.0  # $50 in cents if promo is applied

    offer_dict.update({
        "offer_id": offer_id,
        "brand_id": brand_id,
        "product_id": product_id,
        "conversion_count": {
            "total": 0,
            "production": 0,
            "test": 0
        },
        "click_count": {
            "total": 0,
            "production": 0,
            "test": 0
        },
        "total_spent": {
            "production": 0.0,
            "test": 0.0,
            "all": 0.0
        },
        "offer_total_budget_spent": 0.0,      # Budget spent for the offer
        "offer_total_promo_spent": 0.0,       # Promo credit spent for the offer
        "offer_total_promo_available":total_promo_available,  # Available budget for the offer
        "promo_applied": promo_applied, # Flag to indicate if promo credit has been applied
        "promo_conversions_left": 5 if promo_applied else 0,  # 5 free conversions if promo is applied, else 0 for "pay per conversion"
        "trust_score": 100,             # Set trust_score to 100 for all new offers
        "created_at":firestore.SERVER_TIMESTAMP,
        "active": offer_dict.get("active", True),
        "reward_note": offer_dict.get("reward_note") or f"${offer_dict['payout']['amount']} per {offer_dict.get('goal', 'conversion')}"
    })

    # Add offer incentive if provided
    if hasattr(payload.offer, 'offer_incentive') and payload.offer.offer_incentive:
        offer_dict["offer_incentive"] = {
            "type": payload.offer.offer_incentive.type,
            "headline": payload.offer.offer_incentive.headline,
            "details": payload.offer.offer_incentive.details,
            "cta_label": payload.offer.offer_incentive.cta_label
        }

    db.collection("offers").document(offer_id).set(offer_dict)

    # Create a transaction log for budget allocation if promo is applied
    if promo_applied:
        # Second transaction: Allocate budget to offer (debit transaction)
        create_wallet_transaction(
            brand_id=brand_id,
            transaction_type="debit",
            category="budget_allocation",
            amount=5000.0,  # $50 in cents
            description="Budget allocated to offer during onboarding",
            balance_after=0.0,  # After allocation, promo balance is 0
            reference_id=offer_id,
            reference_type="offer"
        )

    # Update onboarding steps
    db.collection("brands").document(brand_id).update({
        "onboarding_steps.offer": True
    })

    # 4. TRACKING
    tracking_data = payload.tracking.model_dump()
    tracking_data["updated_at"] = firestore.SERVER_TIMESTAMP

    # Create tracking object
    tracking = {
        "method": tracking_data["method"],
        "redirect_url": tracking_data.get("redirect_url", ""),
        "target_urls": tracking_data.get("target_urls", [])
    }

    db.collection("offers").document(offer_id).update({
        "tracking": tracking
    })

    # Update brand + product refs
    db.collection("brands").document(brand_id).update({
        "active_offers": firestore.ArrayUnion([offer_id]),
        "active_products": firestore.ArrayUnion([product_id]),
        "onboarding_steps.offer": True,
        "onboarding_status": "tracking"  # Set status to tracking since that's the next step
        # Don't mark onboarding as completed yet - this will be done in the /onboarding/complete endpoint
    })
    db.collection("products").document(product_id).update({
        "active_offers": firestore.ArrayUnion([offer_id])
    })

    return {
        "status": "success",
        "brand_id": brand_id,
        "offer_id": offer_id,
        "product_id": product_id,
        "tracking": tracking,
        "onboarding_completed": False,
        "onboarding_status": "tracking",  # Include the onboarding status in the response
        "product_existed": existing_products_by_slug is not None and len(existing_products_by_slug) > 0
    }

@router.get("/profile")
async def get_brand_profile(user_data=Depends(require_role("brand"))):
    """Get the current brand's profile data"""
    brand_id = user_data["uid"]

    # Get the brand document
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    brand_data = brand_doc.to_dict()

    # Ensure the brand_id is included in the response
    brand_data["brand_id"] = brand_id
    brand_data["uid"] = brand_id
    print(f"Brand data: {brand_data}")
    return brand_data

@router.get("/onboarding/status")
async def get_onboarding_status(user_data=Depends(require_role("brand"))):
    """Get the current onboarding status for a brand"""
    brand_id = user_data["uid"]

    # Get the brand document
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    brand_data = brand_doc.to_dict()

    # Get onboarding steps
    onboarding_steps = brand_data.get("onboarding_steps", {
        "brand": False,
        "product": False,
        "offer": False,
        "tracking": False
    })

    # Get or calculate onboarding status
    onboarding_status = brand_data.get("onboarding_status", "brand")

    # If onboarding_status is not set, calculate it based on steps
    if onboarding_status not in ["completed", "brand", "product", "offer", "tracking"]:
        if not onboarding_steps.get("brand", False):
            onboarding_status = "brand"
        elif not onboarding_steps.get("product", False):
            onboarding_status = "product"
        elif not onboarding_steps.get("offer", False):
            onboarding_status = "offer"
        elif not onboarding_steps.get("tracking", False):
            onboarding_status = "tracking"
        else:
            onboarding_status = "completed"

        # Update the brand document with the calculated status
        brand_ref.update({"onboarding_status": onboarding_status})

    # Calculate next_step
    next_step = None if onboarding_status == "completed" else onboarding_status

    # Get product and offer IDs if available
    product_id = None
    offer_id = None

    # Get the first active product and offer if available
    active_products = brand_data.get("active_products", [])
    if active_products and len(active_products) > 0:
        product_id = active_products[0]

        # Get the first active offer if available
        active_offers = brand_data.get("active_offers", [])
        if active_offers and len(active_offers) > 0:
            offer_id = active_offers[0]
    # Include brand data for auto-fill functionality
    brand_info = {
        "website": brand_data.get("website"),
        "company_name": brand_data.get("company_name"),
        "work_email": brand_data.get("work_email"),
        "logo_url": brand_data.get("logo_url"),
        "headquarters": brand_data.get("headquarters"),
        "application_type": brand_data.get("application_type", "website")
    }

    print({"onboarding_status": onboarding_status,
        "steps": onboarding_steps,
        "next_step": next_step,
        "brand_id": brand_id,
        "product_id": product_id,
        "offer_id": offer_id,
        "brand_info": brand_info})
    return {
        "onboarding_status": onboarding_status,
        "steps": onboarding_steps,
        "next_step": next_step,
        "brand_id": brand_id,
        "product_id": product_id,
        "offer_id": offer_id,
        "brand_info": brand_info
    }

class OnboardingStepUpdate(BaseModel):
    step: Literal["brand", "product", "offer", "tracking"]  # Note: "tracking" was previously "integration" in some places
    completed: bool

@router.post("/onboarding/update-step")
async def update_onboarding_step(payload: OnboardingStepUpdate, user_data=Depends(require_role("brand"))):
    """Update a specific onboarding step"""
    brand_id = user_data["uid"]

    # Get the brand document
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    # Get current onboarding steps
    brand_data = brand_doc.to_dict()
    onboarding_steps = brand_data.get("onboarding_steps", {})

    # Update the specific step
    brand_ref.update({
        f"onboarding_steps.{payload.step}": payload.completed
    })

    # Update the step in our local copy
    onboarding_steps[payload.step] = payload.completed

    # Calculate new onboarding status
    new_status = "completed"

    if not onboarding_steps.get("brand", False):
        new_status = "brand"
    elif not onboarding_steps.get("product", False):
        new_status = "product"
    elif not onboarding_steps.get("offer", False):
        new_status = "offer"
    elif not onboarding_steps.get("tracking", False):
        new_status = "tracking"

    # Update onboarding status
    brand_ref.update({
        "onboarding_status": new_status
    })

    return {
        "status": "success",
        "message": f"Onboarding step '{payload.step}' updated"
    }

class OnboardingCompleteRequest(BaseModel):
    steps_completed: dict = {
        "brand": True,
        "product": True,
        "offer": True,
        "tracking": True  # Note: This was previously named "integration" in some places
    }

@router.get("/{brand_id}/products")
async def get_brand_products(
    brand_id: str,
    user_data=Depends(require_role("brand"))
):
    """Get all products for a specific brand"""
    # Verify the user has access to this brand
    user_brand_id = user_data["uid"]
    if user_brand_id != brand_id and not user_data.get("admin", False):
        raise HTTPException(status_code=403, detail="You don't have permission to access this brand's products")

    # Get the brand document to verify it exists
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    # Query Firestore for products with this brand_id
    query = db.collection("products").where("brand_id", "==", brand_id)
    snapshot = query.stream()
    products = []

    for doc in snapshot:
        product_data = doc.to_dict()
        # Ensure id is included
        if "id" not in product_data:
            product_data["id"] = doc.id
        products.append(product_data)

    # Get the first active offer for each product if available
    for product in products:
        product_id = product.get("id")
        if product_id:
            # Check if the product has active offers
            active_offers = product.get("active_offers", [])
            if active_offers and len(active_offers) > 0:
                # Get the first active offer
                offer_id = active_offers[0]
                product["offer_id"] = offer_id

    return {
        "status": "success",
        "products": products,
        "brand_id": brand_id
    }

@router.post("/{brand_id}/onboarding/complete")
async def complete_onboarding(
    brand_id: str,
    payload: OnboardingCompleteRequest,
    user_data=Depends(require_role("brand"))
):
    """Mark onboarding as completed for a brand"""
    # Verify the user has access to this brand
    user_brand_id = user_data["uid"]
    if user_brand_id != brand_id:
        raise HTTPException(status_code=403, detail="You don't have permission to update this brand")

    # Get the brand document
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    # Get brand data for domain validation
    brand_data = brand_doc.to_dict()
    brand_email = user_data.get("email", "")
    brand_website = brand_data.get("website", "")

    # Email verification validation
    if brand_email and brand_website:
        # Extract domains for comparison
        email_domain = brand_email.split('@')[1].lower() if '@' in brand_email else ""
        website_domain = brand_website.replace("https://", "").replace("http://", "").replace("www.", "").split('/')[0].lower()

        # Check if domains match (email domain should match website domain)
        domains_match = (email_domain == website_domain or
                        email_domain.endswith(f".{website_domain}") or
                        website_domain.endswith(f".{email_domain}"))

        # If domains don't match, require email verification (unless in development)
        if not domains_match:
            # Skip email verification in development environment
            if is_development_environment():
                logger.info(f"Development environment detected - bypassing email verification for brand {brand_id}")
            else:
                # Check if user's email is verified in Firebase
                try:
                    from firebase_admin import auth as firebase_auth
                    user_record = firebase_auth.get_user(user_brand_id)
                    if not user_record.email_verified:
                        raise HTTPException(
                            status_code=422,
                            detail="Email verification required. Please verify your email before completing onboarding."
                        )
                except Exception as e:
                    if "Email verification required" in str(e):
                        raise e
                    # If we can't check verification status, allow completion but log the issue
                    logger.warning(f"Could not verify email verification status for brand {brand_id}: {str(e)}")

    # Domain validation (same as registration)
    if brand_email and brand_website:
        email_domain = brand_email.split('@')[1].lower()
        website_domain = brand_website.replace("https://", "").replace("http://", "").replace("www.", "").split('/')[0].lower()

        # Strict domain validation for onboarding completion
        if email_domain != website_domain and not email_domain.endswith(f".{website_domain}") and not website_domain.endswith(f".{email_domain}"):
            raise HTTPException(
                status_code=422,
                detail=f"Email domain ({email_domain}) must match website domain ({website_domain}) to complete onboarding."
            )

    # Update onboarding steps
    steps_update = {
        "onboarding_steps": {
            "brand": payload.steps_completed.get("brand", True),
            "product": payload.steps_completed.get("product", True),
            "offer": payload.steps_completed.get("offer", True),
            "tracking": payload.steps_completed.get("tracking", True)
        },
        "onboarding_status": "completed",
        "onboarding_completed_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }

    try:
        # Apply promo credit if eligible
        promo_amount = 5000  # $50 in cents

        # Add promo credit to wallet
        wallet_ref = db.collection("wallets").document(brand_id)
        
        # Get current wallet balance
        wallet_doc = await wallet_ref.get()
        if wallet_doc.exists:
            wallet_data = wallet_doc.to_dict()
            current_balance = wallet_data.get("total_available_balance", 0)
        else:
            current_balance = 0

        # Update wallet with promo credit
        await wallet_ref.set({
            "total_available_balance": firestore.Increment(promo_amount / 100),  # Convert to dollars
            "total_promo_available_balance": firestore.Increment(promo_amount / 100),  # Track promo balance separately
            "updated_at": firestore.SERVER_TIMESTAMP
        }, merge=True)

        # Add transaction record
        await create_wallet_transaction(
            brand_id=brand_id,
            transaction_type="credit",
            category="promo_credit",
            amount=promo_amount / 100,  # Convert to dollars
            description="Welcome bonus credit",
            balance_after=current_balance + (promo_amount / 100)  # Convert to dollars
        )

        # Mark promo as applied in brand document
        steps_update["promo_credit_applied"] = True
        steps_update["promo_credit_amount_cents"] = promo_amount

        # Update the brand document with onboarding completion
        await db.collection("brands").document(brand_id).update(steps_update)

        # Get the updated brand data
        updated_brand_doc = await db.collection("brands").document(brand_id).get()
        updated_brand = updated_brand_doc.to_dict()
        updated_brand["id"] = updated_brand_doc.id

    except Exception as e:
        print(f"Error completing onboarding for brand {brand_id}: {str(e)}")

    return {
        "status": "success",
        "message": "Onboarding status updated successfully",
        "onboarding_status": "completed"
    }

@router.get("/{brand_id}/wallet")
async def get_brand_wallet(
    brand_id: str,
    user_data=Depends(require_role("brand"))
):
    """Get wallet information for a specific brand"""
    # Verify the user has access to this brand
    user_brand_id = user_data["uid"]
    if user_brand_id != brand_id and not user_data.get("admin", False):
        raise HTTPException(status_code=403, detail="You don't have permission to access this brand's wallet")

    # Get the wallet document
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        # Create a default wallet if it doesn't exist
        wallet_data = {
            "brand_id": brand_id,
            "total_available_balance": 0.0,
            "total_promo_available_balance": 5000.0,  # $50 in cents
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,  # Track total budget allocated to campaigns
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        wallet_ref.set(wallet_data)
        return {
            "status": "success",
            "wallet": wallet_data,
            "brand_id": brand_id
        }

    wallet_data = wallet_doc.to_dict()
    return {
        "status": "success",
        "wallet": wallet_data,
        "brand_id": brand_id
    }

class WalletUpdateRequest(BaseModel):
    total_available_balance: Optional[float] = None
    total_promo_available_balance: Optional[float] = None
    total_promo_balance_spent: Optional[float] = None
    total_balance_spent: Optional[float] = None
    total_budget_allocated: Optional[float] = None

@router.post("/{brand_id}/wallet/update")
async def update_brand_wallet(
    brand_id: str,
    payload: WalletUpdateRequest,
    user_data=Depends(require_role("brand"))
):
    """Update wallet information for a specific brand"""
    # Verify the user has access to this brand
    user_brand_id = user_data["uid"]
    if user_brand_id != brand_id and not user_data.get("admin", False):
        raise HTTPException(status_code=403, detail="You don't have permission to update this brand's wallet")

    # Get the wallet document
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        # Create a default wallet if it doesn't exist
        wallet_data = {
            "brand_id": brand_id,
            "total_available_balance": 0.0,
            "total_promo_available_balance": 5000.0,  # $50 in cents
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,  # Track total budget allocated to campaigns
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        wallet_ref.set(wallet_data)

    # Update only the fields that are provided
    update_data = {k: v for k, v in payload.model_dump().items() if v is not None}

    if update_data:
        update_data["updated_at"] = firestore.SERVER_TIMESTAMP
        wallet_ref.update(update_data)

    # Get the updated wallet data
    updated_wallet = wallet_ref.get().to_dict()

    return {
        "status": "success",
        "message": "Wallet updated successfully",
        "wallet": updated_wallet,
        "brand_id": brand_id
    }